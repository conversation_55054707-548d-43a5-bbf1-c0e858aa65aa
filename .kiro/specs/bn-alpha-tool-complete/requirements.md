# BN Alpha Tool - 需求文档

## 项目简介

BN Alpha Tool 是一个基于 Next.js 14 的现代化区块链数据分析工具，专门为币安Alpha项目参与者提供钱包收益分析、积分计算、空投管理等功能。

## 需求概述

### 需求1：钱包收益分析系统

**用户故事：** 作为币安Alpha项目参与者，我希望能够分析我的钱包在指定日期的收益情况，包括代币余额、交易记录、积分计算等，以便了解我的投资表现和积分获取情况。

#### 验收标准

1. **WHEN** 用户输入钱包地址和查询日期 **THEN** 系统应该验证地址格式的有效性
2. **WHEN** 用户点击分析按钮 **THEN** 系统应该显示加载状态并开始数据查询
3. **WHEN** 系统查询完成 **THEN** 应该显示包含以下信息的详细报告：
   - 代币余额（BNB、USDT、ZKJ、KOGE、AB、BR等）
   - 代币总价值（以USDT计算）
   - 交易记录（买入/卖出交易）
   - 交易磨损计算（基于实际USDT流入流出：花费的USDT - 得到的USDT，忽略未完成的买入交易）
   - Gas费用统计
   - 积分计算（余额积分 + 交易积分）
4. **WHEN** 用户点击磨损明细 **THEN** 系统应该显示详细的磨损分解：
   - 交易磨损：具体的买入总价值和卖出总价值
   - Gas磨损：Gas费用的详细计算过程
5. **WHEN** 系统遇到API错误 **THEN** 应该显示友好的错误信息并提供重试选项
6. **WHEN** 用户添加多个钱包 **THEN** 系统应该支持批量分析并显示汇总统计

### 需求2：积分计算器

**用户故事：** 作为币安Alpha项目参与者，我希望能够使用积分计算器来预估不同投资策略下的积分收益，以便制定最优的投资计划。

#### 验收标准

1. **WHEN** 用户调整余额滑块 **THEN** 系统应该实时计算并显示余额积分
2. **WHEN** 用户调整交易量滑块 **THEN** 系统应该实时计算并显示交易积分
3. **WHEN** 用户调整空投价值滑块 **THEN** 系统应该实时计算空投收益
4. **WHEN** 用户调整磨损成本滑块 **THEN** 系统应该实时计算净收益
5. **WHEN** 用户查看计算结果 **THEN** 系统应该显示：
   - 每日积分明细
   - 周期总积分
   - 月度空投收益预估
   - 净收益计算（收益 - 磨损成本）

### 需求3：空投历史管理

**用户故事：** 作为币安Alpha项目参与者，我希望能够查看历史空投数据和当前进行中的空投提醒，以便及时参与空投活动并了解历史收益。

#### 验收标准

1. **WHEN** 用户访问空投页面 **THEN** 系统应该显示当前进行中的空投提醒
2. **WHEN** 空投有具体开始时间 **THEN** 系统应该显示实时倒计时
3. **WHEN** 空投只有日期没有具体时间 **THEN** 系统应该显示"今日开放"状态，不显示倒计时进度
4. **WHEN** 用户查看历史数据 **THEN** 系统应该提供两种视图：
   - 数据表格：详细的空投记录列表
   - 历史曲线：积分门槛和收益趋势图表
5. **WHEN** 用户查看空投详情 **THEN** 系统应该显示：
   - 代币名称和类型（Alpha/TGE）
   - 积分门槛要求
   - 空投数量和当前价格
   - 参与人数统计
   - 收益计算

### 需求4：未完成交易处理（买入卖出算一次完整交易）

**用户故事：** 作为币安Alpha项目参与者，完整的刷一笔交易包含买入和卖出两个方向。我希望系统能够智能识别未完成的交易（只买入未卖出），并在计算磨损时忽略这些交易，以便获得更准确的交易磨损数据。

#### 验收标准

1. **WHEN** 用户的最后一笔交易是买入且买入数量比卖出多 **THEN** 系统应该忽略最后一笔买入交易
2. **WHEN** 系统忽略未完成交易 **THEN** 应该在日志中记录被忽略的交易价值
3. **WHEN** 计算交易磨损 **THEN** 系统应该使用公式：完整买入价值 - 所有卖出价值
4. **WHEN** 用户只有买入没有卖出 **THEN** 系统应该显示磨损为0，并提示交易未完成
5. **WHEN** 用户查看交易详情 **THEN** 系统应该清楚标识哪些交易被计入磨损计算，哪些被忽略

### 需求5：规则说明系统

**用户故事：** 作为币安Alpha项目参与者，我希望能够了解积分计算规则、交易识别标准等详细说明，以便更好地理解分析结果。

#### 验收标准

1. **WHEN** 用户访问规则说明页面 **THEN** 系统应该显示完整的规则文档
2. **WHEN** 用户查看积分规则 **THEN** 系统应该说明：
   - 余额积分计算方法
   - 交易积分计算方法
   - BSC链加成规则
3. **WHEN** 用户查看交易识别规则 **THEN** 系统应该说明：
   - 有效交易的判断标准（基于配置的交易对过滤 + 只统计买入交易）
   - 交易量计算方法（bought.usdAmount × volumeMultiplier）
   - 交易笔数统计（只统计买入交易笔数）
   - BSC链加成规则（volumeMultiplier = 2）
   - 交易磨损的计算方法（基于实际USDT流入流出：花费的USDT - 得到的USDT，忽略未完成交易）
   - Gas费用的统计方式（交易笔数 × 平均Gas费）

### 需求6：系统性能要求

**用户故事：** 作为系统用户，我希望系统能够快速响应并稳定运行，即使在处理大量数据时也能保持良好的性能。

#### 验收标准

1. **WHEN** 用户查询单个钱包 **THEN** 系统应该在30秒内返回结果
2. **WHEN** 用户批量查询多个钱包 **THEN** 系统应该支持并行处理并显示进度
3. **WHEN** 系统遇到API限流 **THEN** 应该自动重试并使用备用API密钥
4. **WHEN** 用户重复查询相同数据 **THEN** 系统应该使用缓存提高响应速度
5. **WHEN** 系统负载较高 **THEN** 应该保持稳定运行不出现崩溃

### 需求7：交易详情和积分等级指导

**用户故事：** 作为币安Alpha项目参与者，我希望能够查看详细的交易记录，并了解距离下个积分等级还需要多少交易量，以便制定更精准的交易策略。

#### 验收标准

1. **WHEN** 用户点击查看交易详情 **THEN** 系统应该打开交易详情模态框显示：
   - 所有有效交易的详细列表
   - 交易哈希、交易对、交易数量、USDT价值
   - 交易时间和区块链浏览器链接
2. **WHEN** 用户查看交易统计 **THEN** 系统应该显示：
   - 总交易量和平均交易额
   - 交易次数和BSC积分加成
   - 当前交易积分和积分等级
3. **WHEN** 用户查看积分等级指导 **THEN** 系统应该显示：
   - 距离下个积分等级还差多少真实交易量
   - 考虑BSC链2倍加成效果的计算
   - 积分等级对照表和计算公式说明
4. **WHEN** 用户的交易积分已达到最高等级 **THEN** 系统应该显示"已达到最高等级"的提示
5. **WHEN** 用户查看磨损详情 **THEN** 系统应该显示：
   - 交易磨损的具体计算过程（买入总价值 - 卖出总价值）
   - Gas磨损的详细统计和计算方法

### 需求8：用户体验要求

**用户故事：** 作为系统用户，我希望界面友好、操作简单，能够快速上手使用各项功能。

#### 验收标准

1. **WHEN** 用户首次访问 **THEN** 系统应该显示清晰的导航和功能说明
2. **WHEN** 用户进行操作 **THEN** 系统应该提供实时反馈和状态提示
3. **WHEN** 用户遇到错误 **THEN** 系统应该显示友好的错误信息和解决建议
4. **WHEN** 用户在移动设备上访问 **THEN** 界面应该自适应并保持良好的可用性
5. **WHEN** 用户需要帮助 **THEN** 系统应该提供详细的使用说明和规则解释