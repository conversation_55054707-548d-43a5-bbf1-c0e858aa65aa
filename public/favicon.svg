<svg xmlns="http://www.w3.org/2000/svg" width="32" height="32" viewBox="0 0 32 32">
  <defs>
    <linearGradient id="grad1" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#F0B90B;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#B8860B;stop-opacity:1" />
    </linearGradient>
  </defs>
  
  <!-- 主菱形背景 - 占满整个区域 -->
  <polygon points="16,1 30,15 16,29 2,15" fill="url(#grad1)" stroke="#8B6914" stroke-width="0.5"/>
  
  <!-- 内层菱形装饰 -->
  <polygon points="16,5 26,15 16,25 6,15" fill="#FFD700" opacity="0.5"/>
  
  <!-- Alpha符号 - 更大更粗 -->
  <text x="16" y="20" text-anchor="middle" fill="#1A1A1A" font-family="Arial, sans-serif" font-size="16" font-weight="900">α</text>
  
  <!-- 小装饰点 -->
  <circle cx="16" cy="9" r="1.5" fill="#FFFFFF" opacity="0.8"/>
  <circle cx="16" cy="23" r="1.5" fill="#FFFFFF" opacity="0.8"/>
</svg> 